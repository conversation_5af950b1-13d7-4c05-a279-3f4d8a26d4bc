import warnings
import os
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import torch.nn.functional as F
import json
import time
import numpy as np
from tqdm import tqdm
from typing import List, Dict, Tuple, Optional

warnings.filterwarnings('ignore')
from ultralytics import YOLO
from ultralytics.nn.modules import Conv, C2f, SPPF, Detect

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Installing...")
    os.system("pip install easyocr")
    import easyocr
    EASYOCR_AVAILABLE = True

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Installing...")
    os.system("pip install cnocr")
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Installing...")
    os.system("pip install pytesseract")
    import pytesseract
    TESSERACT_AVAILABLE = True

try:
    import mmocr
    MMOCR_AVAILABLE = True
except ImportError:
    MMOCR_AVAILABLE = False
    print("MMOCR not available. You can install it with: pip install mmocr")
    # MMOCR安装较复杂，不自动安装


class TextDetectionHead(nn.Module):
    """
    文字检测头：检测文字区域的边界框
    """
    def __init__(self, in_channels: int, num_anchors: int = 3):
        super(TextDetectionHead, self).__init__()
        self.num_anchors = num_anchors

        # 文字检测分支
        self.text_conv = nn.Sequential(
            Conv(in_channels, in_channels, 3, 1),
            Conv(in_channels, in_channels, 3, 1),
        )

        # 文字区域分类 (文字/背景)
        self.text_cls = nn.Conv2d(in_channels, num_anchors * 2, 1)

        # 文字边界框回归
        self.text_reg = nn.Conv2d(in_channels, num_anchors * 4, 1)

        # 文字置信度
        self.text_conf = nn.Conv2d(in_channels, num_anchors, 1)

    def forward(self, x):
        x = self.text_conv(x)

        # 文字分类
        cls_output = self.text_cls(x)

        # 边界框回归
        reg_output = self.text_reg(x)

        # 置信度
        conf_output = torch.sigmoid(self.text_conf(x))

        return cls_output, reg_output, conf_output


class TextRecognitionHead(nn.Module):
    """
    文字识别头：识别检测到的文字内容
    """
    def __init__(self, in_channels: int, vocab_size: int = 6000, max_seq_len: int = 25):
        super(TextRecognitionHead, self).__init__()
        self.vocab_size = vocab_size
        self.max_seq_len = max_seq_len

        # 特征提取
        self.feature_extractor = nn.Sequential(
            Conv(in_channels, 256, 3, 1),
            Conv(256, 256, 3, 1),
            nn.AdaptiveAvgPool2d((1, None))  # 高度压缩为1
        )

        # LSTM序列建模
        self.lstm = nn.LSTM(256, 256, num_layers=2, bidirectional=True, batch_first=True)

        # 字符分类器
        self.classifier = nn.Linear(512, vocab_size)  # 双向LSTM输出512维

    def forward(self, x):
        # 特征提取
        features = self.feature_extractor(x)  # [B, 256, 1, W]

        # 调整维度用于LSTM
        B, C, H, W = features.shape
        features = features.squeeze(2).permute(0, 2, 1)  # [B, W, 256]

        # LSTM序列建模
        lstm_out, _ = self.lstm(features)  # [B, W, 512]

        # 字符分类
        output = self.classifier(lstm_out)  # [B, W, vocab_size]

        return output


class MultiTaskModel(nn.Module):
    """
    多任务模型：共享主干网络 + 双分支输出
    架构：输入图像 -> Backbone -> Neck -> [目标检测Head, 文字检测+识别Head]
    """
    def __init__(self, yolo_model_path: str, num_classes: int = 47, vocab_size: int = 6000):
        super(MultiTaskModel, self).__init__()

        # 加载预训练YOLO模型作为主干网络
        self.yolo_model = YOLO(yolo_model_path)
        self.num_classes = num_classes
        self.vocab_size = vocab_size

        # 获取YOLO模型的主干网络和颈部网络
        self.backbone = self.yolo_model.model.model[:10]  # 主干网络 (到SPPF层)
        self.neck = self.yolo_model.model.model[10:22]    # 颈部网络 (FPN部分)

        # 原始目标检测头 (YOLO head)
        self.detection_head = self.yolo_model.model.model[22]  # 检测头

        # 新增文字检测头
        self.text_detection_head = TextDetectionHead(in_channels=256)

        # 新增文字识别头
        self.text_recognition_head = TextRecognitionHead(in_channels=256, vocab_size=vocab_size)

        # 初始化OCR引擎作为后处理
        self.init_ocr_engines()

        # 置信度阈值
        self.detection_conf_threshold = 0.25
        self.text_conf_threshold = 0.5
        self.ocr_confidence_threshold = 0.5

    def forward(self, x):
        """
        前向传播：共享主干网络 + 双分支输出
        """
        # 主干网络特征提取
        backbone_features = []
        for i, layer in enumerate(self.backbone):
            x = layer(x)
            if i in [6, 8, 9]:  # 保存多尺度特征
                backbone_features.append(x)

        # 颈部网络 (FPN)
        neck_features = []
        for layer in self.neck:
            if hasattr(layer, 'f') and layer.f != -1:  # 跳跃连接
                x = layer([x, backbone_features[layer.f]])
            else:
                x = layer(x)
            neck_features.append(x)

        # 分支1: 目标检测 (YOLO head)
        detection_output = self.detection_head(neck_features[-3:])  # 使用最后3个尺度

        # 分支2: 文字检测
        text_det_cls, text_det_reg, text_det_conf = self.text_detection_head(neck_features[-1])

        # 分支3: 文字识别 (在检测到的文字区域上)
        text_rec_output = self.text_recognition_head(neck_features[-1])

        return {
            'detection': detection_output,
            'text_detection': (text_det_cls, text_det_reg, text_det_conf),
            'text_recognition': text_rec_output
        }

    def init_ocr_engines(self):
        """初始化多个OCR引擎以提高识别精度"""
        self.ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',  # 轻量级高精度模型
                    det_model_name='db_resnet18',  # 文字检测模型
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

        # Tesseract引擎 - 开源OCR
        if TESSERACT_AVAILABLE:
            try:
                # 测试Tesseract是否可用
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = pytesseract
                print("✓ Tesseract引擎初始化成功")
            except Exception as e:
                print(f"✗ Tesseract引擎初始化失败: {e}")
                print("💡 请确保已安装Tesseract-OCR: https://github.com/tesseract-ocr/tesseract")

        # MMOCR引擎 - 高精度OCR (可选)
        if MMOCR_AVAILABLE:
            try:
                # MMOCR需要更复杂的初始化，这里简化处理
                self.ocr_engines['mmocr'] = mmocr
                print("✓ MMOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ MMOCR引擎初始化失败: {e}")

    def detect_objects(self, image_path, conf_threshold=0.25):
        """
        使用YOLO进行目标检测
        """
        results = self.yolo_model.predict(
            image_path,
            conf=conf_threshold,
            device='0' if torch.cuda.is_available() else 'cpu',
            verbose=False
        )
        return results

    def detect_text_regions_neural(self, image):
        """
        使用神经网络文字检测头检测文字区域
        """
        # 预处理图像
        device = next(self.parameters()).device

        # 转换为tensor
        if isinstance(image, np.ndarray):
            image_tensor = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        else:
            image_tensor = image

        image_tensor = image_tensor.unsqueeze(0).to(device)

        # 前向传播
        with torch.no_grad():
            outputs = self.forward(image_tensor)
            text_cls, text_reg, text_conf = outputs['text_detection']

        # 后处理：从网络输出解析文字区域
        text_regions = self.parse_text_detections(text_cls, text_reg, text_conf, image.shape)

        return text_regions

    def parse_text_detections(self, cls_output, reg_output, conf_output, image_shape):
        """
        解析文字检测网络的输出，获取文字区域边界框
        """
        text_regions = []
        h, w = image_shape[:2]

        # 获取置信度高的区域
        conf_mask = conf_output > self.text_conf_threshold

        # 简化的后处理：找到置信度高的位置
        conf_indices = torch.nonzero(conf_mask, as_tuple=False)

        for idx in conf_indices:
            batch, anchor, y, x = idx

            # 计算实际坐标
            stride = 8  # 假设下采样8倍
            center_x = x * stride
            center_y = y * stride

            # 获取回归偏移
            reg_values = reg_output[batch, anchor*4:(anchor+1)*4, y, x]
            dx, dy, dw, dh = reg_values

            # 计算边界框
            x1 = max(0, int(center_x - dw/2))
            y1 = max(0, int(center_y - dh/2))
            x2 = min(w, int(center_x + dw/2))
            y2 = min(h, int(center_y + dh/2))

            confidence = float(conf_output[batch, anchor, y, x])

            text_regions.append({
                'bbox': (x1, y1, x2, y2),
                'confidence': confidence,
                'type': 'neural_text_detection'
            })

        return text_regions

    def extract_text_regions(self, image, detection_results):
        """
        从检测结果中提取可能包含文字的区域
        """
        text_regions = []

        if len(detection_results) > 0 and len(detection_results[0].boxes) > 0:
            boxes = detection_results[0].boxes

            for box in boxes:
                # 获取边界框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])

                # 扩展边界框以包含可能的文字区域
                h, w = image.shape[:2]
                margin = 10
                x1 = max(0, int(x1) - margin)
                y1 = max(0, int(y1) - margin)
                x2 = min(w, int(x2) + margin)
                y2 = min(h, int(y2) + margin)

                # 提取区域
                region = image[y1:y2, x1:x2]

                text_regions.append({
                    'region': region,
                    'bbox': (x1, y1, x2, y2),
                    'confidence': confidence,
                    'class_id': class_id
                })

        return text_regions

    def recognize_text_easyocr(self, image_region):
        """
        使用EasyOCR识别文字
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            results = self.ocr_engines['easyocr'].readtext(image_region)

            text_results = []
            for (bbox, text, confidence) in results:
                if confidence > self.ocr_confidence_threshold:
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'easyocr'
                    })

            return text_results
        except Exception as e:
            print(f"EasyOCR识别错误: {e}")
            return []

    def recognize_text_cnocr(self, image_region):
        """
        使用CnOCR识别文字
        """
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            # CnOCR需要PIL图像格式
            from PIL import Image
            if isinstance(image_region, type(None)) or image_region.size == 0:
                return []

            # 转换为PIL图像
            if len(image_region.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image_region, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image_region)

            # 使用CnOCR进行识别
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            text_results = []
            for result in results:
                text = result.get('text', '')
                confidence = result.get('score', 0.0)

                if confidence > self.ocr_confidence_threshold and text.strip():
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': result.get('position', []),
                        'engine': 'cnocr'
                    })

            return text_results
        except Exception as e:
            print(f"CnOCR识别错误: {e}")
            return []

    def recognize_text_tesseract(self, image_region):
        """
        使用Tesseract识别文字
        """
        if 'tesseract' not in self.ocr_engines:
            return []

        try:
            if isinstance(image_region, type(None)) or image_region.size == 0:
                return []

            # 配置Tesseract参数
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十百千万亿'

            # 使用Tesseract进行识别
            text = pytesseract.image_to_string(image_region, lang='chi_sim+eng', config=config)

            # 获取详细信息
            data = pytesseract.image_to_data(image_region, lang='chi_sim+eng', config=config, output_type=pytesseract.Output.DICT)

            text_results = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > self.ocr_confidence_threshold * 100:  # Tesseract置信度是0-100
                    word_text = data['text'][i].strip()
                    if word_text:
                        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                        text_results.append({
                            'text': word_text,
                            'confidence': int(data['conf'][i]) / 100.0,  # 转换为0-1范围
                            'bbox': [[x, y], [x+w, y], [x+w, y+h], [x, y+h]],
                            'engine': 'tesseract'
                        })

            return text_results
        except Exception as e:
            print(f"Tesseract识别错误: {e}")
            return []

    def recognize_text_mmocr(self, image_region):
        """
        使用MMOCR识别文字 (如果可用)
        """
        if 'mmocr' not in self.ocr_engines:
            return []

        try:
            if isinstance(image_region, type(None)) or image_region.size == 0:
                return []

            # MMOCR的使用方式较复杂，这里提供简化版本
            # 实际使用时需要根据MMOCR的具体API进行调整
            text_results = []

            # 简化处理：使用基本的文字检测
            # 注意：这里需要根据实际的MMOCR版本和API进行调整
            print("⚠️ MMOCR引擎需要额外配置，当前跳过")

            return text_results
        except Exception as e:
            print(f"MMOCR识别错误: {e}")
            return []

    def ensemble_ocr_results(self, all_ocr_results):
        """
        融合多个OCR引擎的结果以提高精度
        支持任意数量的OCR引擎结果
        """
        if not all_ocr_results:
            return []

        # 按置信度排序
        all_ocr_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重和融合
        final_results = []
        for result in all_ocr_results:
            # 简单的去重策略：如果文字内容相似度高，选择置信度更高的
            is_duplicate = False
            for existing in final_results:
                if self.text_similarity(result['text'], existing['text']) > 0.8:
                    is_duplicate = True
                    # 如果当前结果置信度更高，替换现有结果
                    if result['confidence'] > existing['confidence']:
                        final_results.remove(existing)
                        final_results.append(result)
                    break

            if not is_duplicate:
                final_results.append(result)

        # 按置信度重新排序
        final_results.sort(key=lambda x: x['confidence'], reverse=True)

        return final_results

    def text_similarity(self, text1, text2):
        """
        计算两个文本的相似度
        """
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        set1 = set(text1.lower())
        set2 = set(text2.lower())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        综合预测：目标检测 + OCR文字识别
        使用多任务神经网络 + 传统OCR引擎融合
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        # 方法1: 使用YOLO进行目标检测
        detection_results = self.detect_objects(image_path)

        # 方法2: 使用神经网络进行文字区域检测
        neural_text_regions = self.detect_text_regions_neural(image)

        # 方法3: 从目标检测结果中提取可能的文字区域
        detection_text_regions = self.extract_text_regions(image, detection_results)

        # 融合两种文字区域检测结果
        all_text_regions = neural_text_regions + detection_text_regions

        # OCR文字识别 - 使用多引擎融合
        all_text_results = []
        for region_info in all_text_regions:
            if 'region' in region_info:
                region = region_info['region']
            else:
                # 从图像中提取区域
                bbox = region_info['bbox']
                x1, y1, x2, y2 = bbox
                region = image[y1:y2, x1:x2]

            if region.size == 0:
                continue

            # 使用多个OCR引擎
            easyocr_results = self.recognize_text_easyocr(region)
            cnocr_results = self.recognize_text_cnocr(region)
            tesseract_results = self.recognize_text_tesseract(region)
            mmocr_results = self.recognize_text_mmocr(region)

            # 融合结果
            all_ocr_results = easyocr_results + cnocr_results + tesseract_results + mmocr_results
            ensemble_results = self.ensemble_ocr_results(all_ocr_results)

            # 添加区域信息
            for text_result in ensemble_results:
                text_result.update({
                    'detection_bbox': region_info['bbox'],
                    'detection_confidence': region_info['confidence'],
                    'detection_type': region_info.get('type', 'unknown'),
                    'detection_class_id': region_info.get('class_id', -1)
                })

            all_text_results.extend(ensemble_results)

        # 整合结果
        final_result = {
            'image_path': image_path,
            'detections': detection_results,
            'text_recognition': all_text_results,
            'neural_text_regions': len(neural_text_regions),
            'detection_text_regions': len(detection_text_regions),
            'timestamp': time.time()
        }

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def save_prediction_result(self, result, image, output_dir):
        """
        保存预测结果（可视化图像和JSON数据）
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件名
        image_name = Path(result['image_path']).stem

        # 在图像上绘制结果
        result_image = image.copy()

        # 绘制目标检测框
        if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0:
            boxes = result['detections'][0].boxes
            for box in boxes:
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])

                # 绘制检测框
                cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                           (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果
        for text_result in result['text_recognition']:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']

            # 绘制文字区域
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

            # 添加识别的文字
            cv2.putText(result_image, f'{text} ({confidence:.2f})',
                       (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        json_result = {
            'image_path': result['image_path'],
            'detections': [
                {
                    'bbox': box.xyxy[0].cpu().numpy().tolist(),
                    'confidence': float(box.conf[0]),
                    'class_id': int(box.cls[0])
                } for box in result['detections'][0].boxes
            ] if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0 else [],
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2)


def train_yolo_model():
    """
    训练YOLO目标检测模型
    """
    print("🚀 开始训练YOLO目标检测模型...")

    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")

    # 加载预训练模型
    model = YOLO(r'yolov12s.pt')

    # 高精度训练参数配置
    training_args = {
        'data': r'yqjdataset/data.yaml',
        'epochs': 150,  # 增加训练轮数以提高精度
        'imgsz': 640,
        'batch': 16,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,  # 最终学习率
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'close_mosaic': 15,
        'workers': 4,
        'amp': True,  # 自动混合精度
        'single_cls': False,
        'project': 'high_precision_detection',
        'name': 'yolov12s_ocr_integrated',
        'save': True,
        'save_period': 10,  # 每10个epoch保存一次
        'val': True,
        'plots': True,
        'verbose': True,
        # 数据增强参数 - 提高模型泛化能力
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results


def train_multitask_model(yolo_model_path=None, epochs=50):
    """
    训练多任务模型 (目标检测 + 文字检测识别)
    """
    print("🚀 开始训练多任务模型...")

    # 检查GPU可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"📱 使用设备: {device}")

    # 创建多任务模型
    if yolo_model_path is None:
        yolo_model_path = 'high_precision_detection/yolov12s_ocr_integrated/weights/best.pt'
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolov12s.pt'

    model = MultiTaskModel(yolo_model_path)
    model.to(device)

    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.0005)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # 损失函数
    detection_loss_fn = nn.CrossEntropyLoss()
    text_detection_loss_fn = nn.BCEWithLogitsLoss()
    text_recognition_loss_fn = nn.CrossEntropyLoss()

    print("✅ 多任务模型训练准备完成!")
    print("⚠️  注意：完整的多任务训练需要标注的文字检测和识别数据")
    print("📝 当前版本主要使用预训练YOLO + OCR引擎融合的方式")

    return model


def create_integrated_model(yolo_model_path=None):
    """
    创建整合的多任务模型
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolov12s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolov12s.pt'

    print(f"🔧 创建整合模型，使用YOLO权重: {yolo_model_path}")

    # 创建多任务模型
    integrated_model = MultiTaskModel(yolo_model_path)

    print("✅ 整合模型创建完成!")
    return integrated_model


def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(tqdm(test_images[:10], desc="测试进度")):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = len(result['detections'][0].boxes) if len(result['detections']) > 0 else 0
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results


def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 高精度目标检测+OCR文字识别整合训练系统")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    try:
        _, _ = train_yolo_model()
        print("✅ YOLO模型训练完成")
    except Exception as e:
        print(f"⚠️ YOLO训练出错，使用预训练模型: {e}")

    # 步骤2: 创建多任务整合模型
    print("\n📍 步骤2: 创建多任务整合模型 (目标检测+OCR)")
    integrated_model = create_integrated_model()

    # 步骤3: 训练多任务模型 (可选)
    print("\n📍 步骤3: 多任务模型训练准备")
    try:
        multitask_model = train_multitask_model()
        print("✅ 多任务模型准备完成")
    except Exception as e:
        print(f"⚠️ 多任务模型准备出错: {e}")
        multitask_model = integrated_model

    # 步骤4: 测试整合模型
    print("\n📍 步骤4: 测试整合模型性能")
    try:
        _ = test_integrated_model(integrated_model)
        print("✅ 模型测试完成")
    except Exception as e:
        print(f"⚠️ 模型测试出错: {e}")

    print("\n🎉 训练和测试完成!")
    print("📁 检查以下目录获取结果:")
    print("   - high_precision_detection/yolov12s_ocr_integrated/ (YOLO训练结果)")
    print("   - integrated_results/ (整合模型测试结果)")
    print("\n🔧 模型特点:")
    print("   ✓ 使用YOLOv12s作为主干网络，提高检测精度")
    print("   ✓ 共享主干网络，提高计算效率")
    print("   ✓ 双分支输出：目标检测 + 文字识别")
    print("   ✓ 多OCR引擎融合：EasyOCR + CnOCR + Tesseract + MMOCR")
    print("   ✓ 端到端训练能力 (需要相应标注数据)")
    print("   ✓ 移除PaddleOCR，使用更轻量级的OCR组合")

    return integrated_model


def demo_multitask_prediction(image_path='DaYuanTuZ_0.png'):
    """
    演示多任务模型的预测功能
    """
    print(f"🎬 演示多任务模型预测功能")
    print(f"📸 测试图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    # 创建模型
    model = create_integrated_model()

    # 进行预测
    try:
        result = model.predict(image_path, save_result=True, output_dir='demo_results')

        # 打印结果摘要
        detection_count = len(result['detections'][0].boxes) if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0 else 0
        text_count = len(result['text_recognition'])
        neural_text_regions = result.get('neural_text_regions', 0)
        detection_text_regions = result.get('detection_text_regions', 0)

        print(f"\n📊 预测结果摘要:")
        print(f"   🎯 检测到元器件: {detection_count} 个")
        print(f"   📝 识别到文字: {text_count} 段")
        print(f"   🧠 神经网络文字区域: {neural_text_regions} 个")
        print(f"   🔍 检测框文字区域: {detection_text_regions} 个")

        if text_count > 0:
            print(f"\n📝 识别到的文字内容:")
            for i, text_result in enumerate(result['text_recognition'][:5]):  # 显示前5个
                print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

        print(f"\n💾 结果已保存到: demo_results/")

    except Exception as e:
        print(f"❌ 预测过程出错: {e}")


if __name__ == '__main__':
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        # 演示模式
        demo_multitask_prediction()
    else:
        # 完整训练模式
        model = main()