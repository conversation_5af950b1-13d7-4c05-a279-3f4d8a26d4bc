#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检测脚本 - 不使用GUI，直接检测DaYuanTuZ_0.png
"""

import os
import torch
import cv2
import time
from PIL import Image
import json
import numpy as np
from typing import List, Tuple, Dict

def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        print(f"🖥️  GPU总内存: {gpu_memory:.1f} GB")
        free_memory = torch.cuda.memory_reserved(0) / 1024**3
        print(f"💾 GPU可用内存: {free_memory:.1f} GB")
        return gpu_memory
    else:
        print("❌ 未检测到CUDA GPU")
        return 0

def preprocess_image(image_path, target_size=640):
    """预处理图像 - 根据训练尺寸进行适当缩放"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            max_dim = max(width, height)

            print(f"📐 原始图像尺寸: {width} x {height}")

            # 如果图像太大，缩放到合适的训练尺寸
            if max_dim > target_size:
                scale = target_size / max_dim
                new_width = int(width * scale)
                new_height = int(height * scale)

                print(f"🔄 缩放图像到训练兼容尺寸: {new_width} x {new_height} (缩放比例: {scale:.3f})")

                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                temp_path = "temp_resized_detection.jpg"
                resized_img.save(temp_path, quality=95)

                return temp_path, scale
            else:
                print(f"📐 图像尺寸适合，无需缩放")
                return image_path, 1.0

    except Exception as e:
        print(f"❌ 图像预处理错误: {e}")
        return image_path, 1.0

def create_sliding_windows(image_width: int, image_height: int, window_size: int = 640, overlap_ratio: float = 0.2) -> List[Tuple[int, int, int, int]]:
    """
    创建滑动窗口坐标列表

    Args:
        image_width: 图像宽度
        image_height: 图像高度
        window_size: 窗口大小
        overlap_ratio: 重叠比例 (0.0-1.0)

    Returns:
        List of (x1, y1, x2, y2) 窗口坐标
    """
    windows = []
    step_size = int(window_size * (1 - overlap_ratio))

    # 如果图像小于窗口大小，直接返回整个图像
    if image_width <= window_size and image_height <= window_size:
        return [(0, 0, image_width, image_height)]

    # 计算窗口数量
    x_windows = max(1, (image_width - window_size) // step_size + 1)
    y_windows = max(1, (image_height - window_size) // step_size + 1)

    print(f"🔲 创建滑动窗口: {x_windows} x {y_windows} = {x_windows * y_windows} 个窗口")
    print(f"   - 窗口大小: {window_size}x{window_size}")
    print(f"   - 重叠比例: {overlap_ratio:.1%}")
    print(f"   - 步长: {step_size}")

    for y in range(y_windows):
        for x in range(x_windows):
            x1 = x * step_size
            y1 = y * step_size
            x2 = min(x1 + window_size, image_width)
            y2 = min(y1 + window_size, image_height)

            # 确保窗口有足够的大小
            if x2 - x1 >= window_size // 2 and y2 - y1 >= window_size // 2:
                windows.append((x1, y1, x2, y2))

    return windows

def non_max_suppression_custom(detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
    """
    自定义非极大值抑制，去除重复检测

    Args:
        detections: 检测结果列表
        iou_threshold: IoU阈值

    Returns:
        去重后的检测结果
    """
    if not detections:
        return []

    # 按置信度排序
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)

    keep = []
    while detections:
        current = detections.pop(0)
        keep.append(current)

        # 计算与剩余检测框的IoU
        remaining = []
        for det in detections:
            if calculate_iou(current['bbox'], det['bbox']) < iou_threshold:
                remaining.append(det)

        detections = remaining

    return keep

def calculate_iou(box1: List[float], box2: List[float]) -> float:
    """
    计算两个边界框的IoU

    Args:
        box1: [x1, y1, x2, y2]
        box2: [x1, y1, x2, y2]

    Returns:
        IoU值
    """
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])

    if x2 <= x1 or y2 <= y1:
        return 0.0

    intersection = (x2 - x1) * (y2 - y1)
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - intersection

    return intersection / union if union > 0 else 0.0

def detect_with_yolo(image_path, model_path, conf_threshold=0.5, imgsz=640, use_sliding_window=True, overlap_ratio=0.2):
    """
    使用YOLO进行目标检测，支持滑动窗口避免漏检

    Args:
        image_path: 图像路径
        model_path: 模型路径
        conf_threshold: 置信度阈值
        imgsz: 推理尺寸
        use_sliding_window: 是否使用滑动窗口
        overlap_ratio: 滑动窗口重叠比例

    Returns:
        detections: 检测结果列表
        None: 不再返回原始results对象
    """
    try:
        from ultralytics import YOLO
        
        print(f"🔧 加载模型: {model_path}")
        model = YOLO(model_path)
        
        # 检查GPU内存
        gpu_memory = check_gpu_memory()
        
        # 根据GPU内存选择设备和推理尺寸
        if gpu_memory < 8:
            device = 'cpu'
            imgsz = min(imgsz, 640)  # CPU使用较小尺寸
            print(f"⚠️  GPU内存不足，使用CPU，推理尺寸: {imgsz}")
        else:
            device = '0'
            print(f"✅ 使用GPU，推理尺寸: {imgsz}")

        # 确保尺寸是32的倍数
        if imgsz % 32 != 0:
            imgsz = ((imgsz // 32) + 1) * 32
            print(f"📏 调整推理尺寸为32的倍数: {imgsz}")
        print(f"� 将使用原始图像尺寸进行推理（不进行任何缩放）")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 读取原始图像信息
        with Image.open(image_path) as img:
            original_width, original_height = img.size

        print(f"🚀 开始检测...")
        print(f"   - 图像: {image_path}")
        print(f"   - 原始尺寸: {original_width} x {original_height}")
        print(f"   - 推理尺寸: {imgsz}")
        print(f"   - 设备: {device}")
        print(f"   - 置信度: {conf_threshold}")
        print(f"   - 滑动窗口: {'启用' if use_sliding_window else '禁用'}")

        all_detections = []

        # 判断是否使用滑动窗口
        if use_sliding_window and (original_width > imgsz or original_height > imgsz):
            print(f"📐 图像较大，使用滑动窗口检测...")

            # 创建滑动窗口
            windows = create_sliding_windows(original_width, original_height, imgsz, overlap_ratio)

            # 对每个窗口进行检测
            for i, (x1, y1, x2, y2) in enumerate(windows):
                print(f"🔍 处理窗口 {i+1}/{len(windows)}: ({x1}, {y1}, {x2}, {y2})")

                # 裁剪图像窗口
                with Image.open(image_path) as img:
                    window_img = img.crop((x1, y1, x2, y2))
                    temp_window_path = f"temp_window_{i}.jpg"
                    window_img.save(temp_window_path, quality=95)

                try:
                    # 对窗口进行检测
                    results = model.predict(
                        temp_window_path,
                        conf=conf_threshold,
                        imgsz=imgsz,
                        device=device,
                        verbose=False,
                        save=False,
                        show=False,
                        half=False,
                        max_det=1000
                    )

                    # 处理窗口检测结果
                    if len(results) > 0 and len(results[0].boxes) > 0:
                        boxes = results[0].boxes
                        for box in boxes:
                            box_x1, box_y1, box_x2, box_y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            class_id = int(box.cls[0])

                            # 转换坐标到原图坐标系
                            global_x1 = x1 + box_x1
                            global_y1 = y1 + box_y1
                            global_x2 = x1 + box_x2
                            global_y2 = y1 + box_y2

                            detection = {
                                'class_id': class_id,
                                'confidence': confidence,
                                'bbox': [float(global_x1), float(global_y1), float(global_x2), float(global_y2)]
                            }
                            all_detections.append(detection)

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_window_path):
                        os.remove(temp_window_path)

            # 对所有检测结果进行NMS去重
            print(f"🔄 检测完成，进行NMS去重...")
            print(f"   - 原始检测数: {len(all_detections)}")
            all_detections = non_max_suppression_custom(all_detections, iou_threshold=0.5)
            print(f"   - 去重后检测数: {len(all_detections)}")

        else:
            print(f"📐 图像尺寸适中或禁用滑动窗口，使用整图检测...")

            # 预处理图像（根据训练尺寸调整）
            processed_path, scale_factor = preprocess_image(image_path, target_size=imgsz)

            # 进行检测（使用训练兼容的推理尺寸）
            results = model.predict(
                processed_path,
                conf=conf_threshold,
                imgsz=imgsz,
                device=device,
                verbose=False,
                save=False,
                show=False,
                half=False,
                max_det=1000
            )

            # 处理结果
            if len(results) > 0 and len(results[0].boxes) > 0:
                boxes = results[0].boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 如果图像被缩放，需要还原到原始坐标
                    if scale_factor != 1.0:
                        x1, y1, x2, y2 = x1/scale_factor, y1/scale_factor, x2/scale_factor, y2/scale_factor

                    detection = {
                        'class_id': class_id,
                        'confidence': confidence,
                        'bbox': [float(x1), float(y1), float(x2), float(y2)]
                    }
                    all_detections.append(detection)

            # 清理临时文件
            if 'processed_path' in locals() and processed_path != image_path and os.path.exists(processed_path):
                os.remove(processed_path)
                print(f"🗑️  已清理临时文件: {processed_path}")
        
        print(f"✅ 检测完成!")

        # 显示最终结果
        if all_detections:
            print(f"📦 最终检测到 {len(all_detections)} 个目标:")
            for i, detection in enumerate(all_detections):
                bbox = detection['bbox']
                class_id = detection['class_id']
                print(f"  {i+1}. 类别ID: {class_id}, "
                      f"位置: ({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})")
        else:
            print("❌ 未检测到任何目标")

        return all_detections, None
        
    except Exception as e:
        print(f"❌ 检测错误: {e}")
        import traceback
        traceback.print_exc()
        return [], None

def save_results(detections, image_path, output_path="detection_results.json"):
    """保存检测结果"""
    result = {
        'image_path': image_path,
        'timestamp': time.time(),
        'detections': detections,
        'total_detections': len(detections)
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"💾 结果已保存到: {output_path}")

def visualize_results(image_path, detections, output_path="detection_result.jpg"):
    """可视化检测结果"""
    try:
        import cv2
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return
        
        # 绘制检测框
        for detection in detections:
            bbox = detection['bbox']
            class_id = detection['class_id']

            x1, y1, x2, y2 = map(int, bbox)
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"ID:{class_id}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 保存结果图像
        cv2.imwrite(output_path, image)
        print(f"🖼️  可视化结果已保存到: {output_path}")
        
    except Exception as e:
        print(f"❌ 可视化错误: {e}")

def main():
    """主函数"""
    print("🎯 开始直接检测...")
    
    # 配置参数
    image_path = "DaYuanTuZ_0.png"
    model_path = "animal_detection1/yolo11s_exp1/weights/best.pt"
    conf_threshold = 0.5  # 提高置信度阈值过滤低置信度检测
    imgsz = 640  # 训练兼容的推理尺寸
    use_sliding_window = True  # 是否使用滑动窗口
    overlap_ratio = 0.2  # 滑动窗口重叠比例
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📁 图像文件: {image_path}")
    print(f"🤖 模型文件: {model_path}")
    print(f"🔧 检测配置:")
    print(f"   - 置信度阈值: {conf_threshold}")
    print(f"   - 推理尺寸: {imgsz}")
    print(f"   - 滑动窗口: {'启用' if use_sliding_window else '禁用'}")
    if use_sliding_window:
        print(f"   - 重叠比例: {overlap_ratio:.1%}")
    
    # 进行检测
    detections, _ = detect_with_yolo(
        image_path, model_path, conf_threshold, imgsz, use_sliding_window, overlap_ratio
    )
    
    # 保存结果
    if detections:
        save_results(detections, image_path)
        visualize_results(image_path, detections)
        print(f"\n🎉 检测完成! 共检测到 {len(detections)} 个目标")
    else:
        print("\n❌ 未检测到任何目标")

if __name__ == "__main__":
    main()
