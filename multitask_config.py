"""
多任务模型配置文件
配置目标检测和OCR文字识别的相关参数
"""

# 模型路径配置
MODEL_PATHS = {
    'yolo_pretrained': 'yolo11s.pt',
    'yolo_trained': 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt',
    'multitask_save_path': 'multitask_models/',
}

# 数据集配置
DATASET_CONFIG = {
    'data_yaml': 'yqjdataset/data.yaml',
    'train_images': 'yqjdataset/train/images',
    'test_images': 'yqjdataset/test/images',
    'num_classes': 47,
}

# 训练参数配置
TRAINING_CONFIG = {
    'yolo': {
        'epochs': 150,
        'imgsz': 640,
        'batch': 16,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'amp': True,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
    },
    'multitask': {
        'epochs': 50,
        'lr': 0.001,
        'weight_decay': 0.0005,
        'batch_size': 8,
    }
}

# OCR引擎配置
OCR_CONFIG = {
    'engines': ['easyocr', 'cnocr', 'paddleocr'],
    'confidence_threshold': 0.5,
    'similarity_threshold': 0.8,
    'languages': ['ch_sim', 'en'],
}

# 模型架构配置
ARCHITECTURE_CONFIG = {
    'backbone_layers': list(range(10)),  # 主干网络层数
    'neck_layers': list(range(10, 22)),  # 颈部网络层数
    'detection_head_layer': 22,          # 检测头层数
    'text_detection': {
        'in_channels': 256,
        'num_anchors': 3,
    },
    'text_recognition': {
        'in_channels': 256,
        'vocab_size': 6000,
        'max_seq_len': 25,
        'lstm_hidden': 256,
        'lstm_layers': 2,
    }
}

# 推理配置
INFERENCE_CONFIG = {
    'detection_conf_threshold': 0.25,
    'text_conf_threshold': 0.5,
    'nms_threshold': 0.45,
    'max_detections': 1000,
}

# 输出配置
OUTPUT_CONFIG = {
    'results_dir': 'results',
    'demo_dir': 'demo_results',
    'save_images': True,
    'save_json': True,
    'image_format': 'jpg',
}

# 设备配置
DEVICE_CONFIG = {
    'use_gpu': True,
    'gpu_id': 0,
    'mixed_precision': True,
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'detection_color': (0, 255, 0),      # 绿色 - 目标检测框
    'text_color': (255, 0, 0),           # 红色 - 文字区域框
    'font_scale': 0.6,
    'thickness': 2,
    'show_confidence': True,
    'show_class_names': True,
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'save_logs': True,
    'log_file': 'multitask_training.log',
}

def get_config(config_name):
    """
    获取指定的配置
    """
    configs = {
        'model_paths': MODEL_PATHS,
        'dataset': DATASET_CONFIG,
        'training': TRAINING_CONFIG,
        'ocr': OCR_CONFIG,
        'architecture': ARCHITECTURE_CONFIG,
        'inference': INFERENCE_CONFIG,
        'output': OUTPUT_CONFIG,
        'device': DEVICE_CONFIG,
        'visualization': VISUALIZATION_CONFIG,
        'logging': LOGGING_CONFIG,
    }
    
    return configs.get(config_name, {})

def print_config_summary():
    """
    打印配置摘要
    """
    print("🔧 多任务模型配置摘要")
    print("=" * 50)
    print(f"📊 数据集类别数: {DATASET_CONFIG['num_classes']}")
    print(f"🎯 YOLO训练轮数: {TRAINING_CONFIG['yolo']['epochs']}")
    print(f"🧠 多任务训练轮数: {TRAINING_CONFIG['multitask']['epochs']}")
    print(f"🔤 OCR引擎: {', '.join(OCR_CONFIG['engines'])}")
    print(f"📝 文字识别词汇量: {ARCHITECTURE_CONFIG['text_recognition']['vocab_size']}")
    print(f"🖥️  使用GPU: {DEVICE_CONFIG['use_gpu']}")
    print("=" * 50)

if __name__ == '__main__':
    print_config_summary()
